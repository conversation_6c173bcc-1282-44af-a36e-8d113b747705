/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true, // Keep disabled for now to avoid blocking builds
  },
  typescript: {
    ignoreBuildErrors: true, // Keep disabled for now to avoid blocking builds
  },
  images: {
    unoptimized: true,
  },
  // Professional development configuration
  experimental: {
    allowedDevOrigins: [
      'mtbrmg.local:3001',
      'app.mtbrmg.local:3001',
      'dashboard.mtbrmg.local:3001',
      'localhost:3001'
    ],
    // Fix chunk loading issues
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  // API rewrites removed - using direct backend connections for local development
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Performance optimizations
  poweredByHeader: false,
  compress: true,
  // Development optimizations
  reactStrictMode: true,
  // Webpack configuration to fix chunk loading issues
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Fix chunk loading issues in development
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            default: {
              minChunks: 1,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: -10,
              chunks: 'all',
            },
          },
        },
      };
    }
    return config;
  },
}

export default nextConfig
